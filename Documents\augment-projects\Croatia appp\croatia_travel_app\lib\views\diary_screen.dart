import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../models/diary_entry.dart';
import '../models/video_diary.dart';
import '../services/export_service.dart';
import '../services/video_diary_service.dart';
import '../services/seasonal_theme_service.dart';
import '../services/print_service.dart';
import '../services/croatian_culture_service.dart';
import '../widgets/video_diary_widget.dart';
import '../widgets/luxury_watercolor_background.dart';
import '../theme/watercolor_theme.dart';
import 'diary_entry_edit_screen.dart';

// Watercolor textura painter pro pozadí
class WatercolorTexturePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.multiply;

    final random = Random(42); // Fixní seed pro konzistentní výsledek

    // Vytvoření watercolor efektů
    for (int i = 0; i < 15; i++) {
      final center = Offset(
        random.nextDouble() * size.width,
        random.nextDouble() * size.height,
      );

      final radius = 50 + random.nextDouble() * 100;

      paint.color = [
        WatercolorTheme.softPeach.withValues(alpha: 0.1),
        WatercolorTheme.adriaticBlue.withValues(alpha: 0.08),
        WatercolorTheme.elegantTeal.withValues(alpha: 0.06),
        WatercolorTheme.warmCream.withValues(alpha: 0.12),
      ][random.nextInt(4)];

      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class DiaryScreen extends StatefulWidget {
  const DiaryScreen({super.key});

  @override
  State<DiaryScreen> createState() => _DiaryScreenState();
}

class _DiaryScreenState extends State<DiaryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ExportService _exportService = ExportService();
  final VideoDiaryService _videoService = VideoDiaryService();
  final SeasonalThemeService _themeService = SeasonalThemeService();
  final PrintService _printService = PrintService();
  final CroatianCultureService _cultureService = CroatianCultureService();

  List<DiaryEntry> _entries = [];
  List<DiaryEntry> _filteredEntries = [];
  List<VideoDiary> _videos = [];
  bool _isLoading = true;
  String _searchQuery = '';
  DiaryMood? _selectedMood;
  DateTime? _selectedDate;
  SeasonalTheme _currentTheme = SeasonalTheme.summer;

  // Kalendář proměnné
  DateTime _currentMonth = DateTime.now();
  DateTime? _selectedCalendarDate;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 5,
      vsync: this,
    ); // Přidán tab pro video
    _initializeServices();
    _loadDiaryEntries();
  }

  Future<void> _initializeServices() async {
    await _videoService.initialize();
    await _themeService.initialize();
    // CroatianCultureService nemá initialize metodu

    // Poslouchání změn tématu
    _themeService.themeStream.listen((theme) {
      if (mounted) {
        setState(() {
          _currentTheme = theme;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadDiaryEntries() async {
    setState(() => _isLoading = true);

    // Simulace načtení dat - v reálné aplikaci by se načítalo z databáze
    await Future.delayed(const Duration(milliseconds: 500));

    final mockEntries = [
      DiaryEntry(
        id: '1',
        title: 'První den v Dubrovniku',
        content:
            'Dnes jsem dorazil do nádherného Dubrovniku. Město je úžasné, staré hradby jsou impozantní. Prošel jsem si celé historické centrum a vyfotil spoustu krásných míst.',
        date: DateTime.now().subtract(const Duration(days: 2)),
        location: 'Dubrovnik, Chorvatsko',
        latitude: 42.6507,
        longitude: 18.0944,
        photos: ['photo1.jpg', 'photo2.jpg'],
        voiceNotes: ['voice1'],
        tags: ['dubrovnik', 'hradby', 'historie'],
        mood: DiaryMood.excited,
        weather: 'Slunečno, 28°C',
        rating: 5.0,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      DiaryEntry(
        id: '2',
        title: 'Výlet na ostrov Lokrum',
        content:
            'Krásný den strávený na ostrově Lokrum. Navštívil jsem botanickou zahradu a vykoupal se v krásném moři. Večer jsme měli skvělou večeři v místní restauraci.',
        date: DateTime.now().subtract(const Duration(days: 1)),
        location: 'Lokrum, Chorvatsko',
        latitude: 42.6167,
        longitude: 18.1167,
        photos: ['photo3.jpg'],
        tags: ['lokrum', 'ostrov', 'moře'],
        mood: DiaryMood.relaxed,
        weather: 'Částečně oblačno, 26°C',
        rating: 4.5,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];

    setState(() {
      _entries = mockEntries;
      _filteredEntries = mockEntries;
      _isLoading = false;
    });
  }

  void _filterEntries() {
    setState(() {
      _filteredEntries = _entries.where((entry) {
        final matchesSearch =
            _searchQuery.isEmpty ||
            entry.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            entry.content.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            entry.tags.any(
              (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
            );

        final matchesMood =
            _selectedMood == null || entry.mood == _selectedMood;

        final matchesDate =
            _selectedDate == null ||
            (entry.date.year == _selectedDate!.year &&
                entry.date.month == _selectedDate!.month &&
                entry.date.day == _selectedDate!.day);

        return matchesSearch && matchesMood && matchesDate;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Krásné watercolor pozadí
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  WatercolorTheme.warmCream,
                  WatercolorTheme.softPeach.withValues(alpha: 0.4),
                  WatercolorTheme.adriaticBlue.withValues(alpha: 0.3),
                  WatercolorTheme.coastalMist.withValues(alpha: 0.2),
                ],
                stops: const [0.0, 0.3, 0.7, 1.0],
              ),
            ),
          ),

          // Watercolor textury
          Positioned.fill(
            child: CustomPaint(painter: WatercolorTexturePainter()),
          ),

          // Hlavní obsah
          SafeArea(child: _buildMainDiaryContent()),
        ],
      ),
    );
  }

  // Hlavní obsah podle vašeho obrázku
  Widget _buildMainDiaryContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Header podle obrázku
          Container(
            padding: const EdgeInsets.fromLTRB(24, 40, 24, 30),
            child: Column(
              children: [
                Text(
                  'Adriatic Diary',
                  style: WatercolorTheme.headingLarge.copyWith(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: WatercolorTheme.deepNavy,
                    letterSpacing: 1.0,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Luxusní a elegantní rozhraní',
                  style: WatercolorTheme.bodyLarge.copyWith(
                    fontSize: 18,
                    color: WatercolorTheme.sunsetOrange,
                    fontStyle: FontStyle.italic,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Telefon mockup podle vašeho obrázku
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            child: _buildPhoneMockup(),
          ),

          const SizedBox(height: 40),

          // Menu tlačítka dole
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: _buildBottomMenuButtons(),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  // Telefon mockup podle vašeho obrázku
  Widget _buildPhoneMockup() {
    return Container(
      width: 280,
      height: 500,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(35),
        boxShadow: [
          BoxShadow(
            color: WatercolorTheme.deepNavy.withValues(alpha: 0.3),
            blurRadius: 30,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: Container(
        margin: const EdgeInsets.all(3),
        decoration: BoxDecoration(
          color: WatercolorTheme.pureWhite,
          borderRadius: BorderRadius.circular(32),
        ),
        child: _buildPhoneContent(),
      ),
    );
  }

  // Obsah telefonu podle vašeho obrázku
  Widget _buildPhoneContent() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(32),
      child: Column(
        children: [
          // Status bar
          Container(
            height: 30,
            color: WatercolorTheme.pureWhite,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Padding(
                  padding: EdgeInsets.only(left: 20),
                  child: Text(
                    '9:41',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: Row(
                    children: [
                      Icon(
                        Icons.signal_cellular_4_bar,
                        size: 16,
                        color: Colors.black,
                      ),
                      const SizedBox(width: 4),
                      Icon(Icons.wifi, size: 16, color: Colors.black),
                      const SizedBox(width: 4),
                      Icon(Icons.battery_full, size: 16, color: Colors.black),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Hlavní obsah telefonu podle vašeho obrázku
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    WatercolorTheme.warmCream,
                    WatercolorTheme.softPeach.withValues(alpha: 0.3),
                    WatercolorTheme.coastalMist.withValues(alpha: 0.2),
                  ],
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // "Deníkový zápis" nadpis
                    Text(
                      'Deníkový zápis',
                      style: WatercolorTheme.headingMedium.copyWith(
                        fontSize: 20,
                        color: WatercolorTheme.deepNavy,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 20),

                    // Mikrofon ikona
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: WatercolorTheme.sunsetOrange,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: WatercolorTheme.sunsetOrange.withValues(
                              alpha: 0.3,
                            ),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.mic,
                        color: WatercolorTheme.pureWhite,
                        size: 28,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // "Naslouchá..." text
                    Text(
                      'Naslouchá...',
                      style: WatercolorTheme.bodyMedium.copyWith(
                        color: WatercolorTheme.richCharcoal.withValues(
                          alpha: 0.7,
                        ),
                        fontStyle: FontStyle.italic,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Text deníkového záznamu
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: WatercolorTheme.pureWhite.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: WatercolorTheme.elegantTeal.withValues(
                            alpha: 0.2,
                          ),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'Dnes jsme jeli trajektem na ostrov Hvar a měli jsme úžasný den na pláži.',
                        style: WatercolorTheme.bodyMedium.copyWith(
                          color: WatercolorTheme.richCharcoal,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 20),

                    // "Napište shrnutí dne"
                    Text(
                      'Napište shrnutí dne',
                      style: WatercolorTheme.bodyMedium.copyWith(
                        color: WatercolorTheme.deepNavy,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Tagy nálady
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildMoodTag('Šťastný', WatercolorTheme.sunsetOrange),
                        const SizedBox(width: 8),
                        _buildMoodTag('Veselý', WatercolorTheme.elegantTeal),
                        const SizedBox(width: 8),
                        _buildMoodTag('Pokojný', WatercolorTheme.adriaticBlue),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Spodní menu tlačítka
  Widget _buildBottomMenuButtons() {
    return Row(
      children: [
        // AI Asistent
        Expanded(
          child: _buildMenuButton(
            icon: Icons.smart_toy,
            label: 'AI Asistent',
            color: WatercolorTheme.elegantTeal,
            onTap: () => Navigator.pushNamed(context, '/ai-assistant'),
          ),
        ),

        const SizedBox(width: 12),

        // Profil
        Expanded(
          child: _buildMenuButton(
            icon: Icons.person,
            label: 'Profil',
            color: WatercolorTheme.sunsetOrange,
            onTap: () => Navigator.pushNamed(context, '/profile'),
          ),
        ),

        const SizedBox(width: 12),

        // Menu
        Expanded(
          child: _buildMenuButton(
            icon: Icons.menu,
            label: 'Menu',
            color: WatercolorTheme.adriaticBlue,
            onTap: () => _showMainMenu(),
          ),
        ),
      ],
    );
  }

  // Menu tlačítka - AI, Profil, Menu
  Widget _buildMenuButtons() {
    return Column(
      children: [
        Text(
          'Rychlé akce',
          style: WatercolorTheme.headingMedium.copyWith(
            color: WatercolorTheme.deepNavy,
            fontWeight: FontWeight.w600,
          ),
        ),

        const SizedBox(height: 16),

        Row(
          children: [
            // AI Asistent
            Expanded(
              child: _buildMenuButton(
                icon: Icons.smart_toy,
                label: 'AI Asistent',
                color: WatercolorTheme.elegantTeal,
                onTap: () => Navigator.pushNamed(context, '/ai-assistant'),
              ),
            ),

            const SizedBox(width: 12),

            // Profil
            Expanded(
              child: _buildMenuButton(
                icon: Icons.person,
                label: 'Profil',
                color: WatercolorTheme.sunsetOrange,
                onTap: () => Navigator.pushNamed(context, '/profile'),
              ),
            ),

            const SizedBox(width: 12),

            // Menu
            Expanded(
              child: _buildMenuButton(
                icon: Icons.menu,
                label: 'Menu',
                color: WatercolorTheme.adriaticBlue,
                onTap: () => _showMainMenu(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Jednotlivé menu tlačítko
  Widget _buildMenuButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: WatercolorTheme.pureWhite.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(color: color, shape: BoxShape.circle),
              child: Icon(icon, color: WatercolorTheme.pureWhite, size: 24),
            ),

            const SizedBox(height: 8),

            Text(
              label,
              style: WatercolorTheme.bodySmall.copyWith(
                color: WatercolorTheme.deepNavy,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Zobrazit hlavní menu
  void _showMainMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: WatercolorTheme.pureWhite,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    'Hlavní menu',
                    style: WatercolorTheme.headingMedium.copyWith(
                      color: WatercolorTheme.deepNavy,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Menu položky
                  _buildMenuListItem(Icons.map, 'Mapa', () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/map');
                  }),

                  _buildMenuListItem(Icons.camera_alt, 'Fotoaparát', () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/camera');
                  }),

                  _buildMenuListItem(
                    Icons.account_balance_wallet,
                    'Peněženka',
                    () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, '/wallet');
                    },
                  ),

                  _buildMenuListItem(Icons.people, 'Komunita', () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/community');
                  }),

                  _buildMenuListItem(Icons.settings, 'Nastavení', () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/settings');
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Položka v menu
  Widget _buildMenuListItem(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: WatercolorTheme.elegantTeal),
      title: Text(
        title,
        style: WatercolorTheme.bodyMedium.copyWith(
          color: WatercolorTheme.deepNavy,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: WatercolorTheme.richCharcoal.withValues(alpha: 0.5),
        size: 16,
      ),
      onTap: onTap,
    );
  }

  // Mood tag widget
  Widget _buildMoodTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Text(
        text,
        style: WatercolorTheme.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
          fontSize: 12,
        ),
      ),
    );
  }

  // Hlavní obsah inspirovaný mockupy
  Widget _buildMockupInspiredContent() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header podle mockupů
            Text(
              'ADRIATIC DIARY',
              style: WatercolorTheme.headingLarge.copyWith(
                color: WatercolorTheme.deepNavy,
                fontSize: 28,
                fontWeight: FontWeight.w700,
                letterSpacing: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            Text(
              'Personal Travel Diary for Croatia',
              style: WatercolorTheme.bodyLarge.copyWith(
                color: WatercolorTheme.sunsetOrange,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // Hlavní deníkový obsah podle mockupů
            _buildDiaryEntryContent(),

            const SizedBox(height: 32),

            // Boční karty podle mockupů
            Row(
              children: [
                Expanded(child: _buildSmartRemindersCard()),
                const SizedBox(width: 16),
                Expanded(child: _buildCalendarCard()),
              ],
            ),

            const SizedBox(height: 32),

            // Navigační tlačítka
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  // Hlavní deníkový obsah podle mockupů
  Widget _buildDiaryEntryContent() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: WatercolorTheme.pureWhite.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: WatercolorTheme.deepNavy.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Nadpis podle mockupu
          Text(
            'Deníkový zápis',
            style: WatercolorTheme.headingMedium.copyWith(
              fontSize: 22,
              color: WatercolorTheme.deepNavy,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Mikrofon ikona podle mockupu
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              color: WatercolorTheme.sunsetOrange,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: WatercolorTheme.sunsetOrange.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Icon(Icons.mic, color: WatercolorTheme.pureWhite, size: 32),
          ),

          const SizedBox(height: 16),

          // "Naslouchá..." text podle mockupu
          Text(
            'Naslouchá...',
            style: WatercolorTheme.bodyMedium.copyWith(
              color: WatercolorTheme.richCharcoal.withValues(alpha: 0.7),
              fontStyle: FontStyle.italic,
            ),
          ),

          const SizedBox(height: 24),

          // Text deníkového záznamu podle mockupu
          Container(
            padding: const EdgeInsets.all(18),
            decoration: BoxDecoration(
              color: WatercolorTheme.warmCream.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: WatercolorTheme.elegantTeal.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              'Dnes jsme jeli trajektem na ostrov Hvar a měli jsme úžasný den na pláži.',
              style: WatercolorTheme.bodyMedium.copyWith(
                color: WatercolorTheme.richCharcoal,
                height: 1.6,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 20),

          // "Napište shrnutí dne" podle mockupu
          Text(
            'Napište shrnutí dne',
            style: WatercolorTheme.bodyMedium.copyWith(
              color: WatercolorTheme.deepNavy,
              fontWeight: FontWeight.w500,
            ),
          ),

          const SizedBox(height: 16),

          // Tagy nálady podle mockupu
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildMoodTag('Šťastný', WatercolorTheme.sunsetOrange),
              const SizedBox(width: 12),
              _buildMoodTag('Veselý', WatercolorTheme.elegantTeal),
              const SizedBox(width: 12),
              _buildMoodTag('Pokojný', WatercolorTheme.adriaticBlue),
            ],
          ),
        ],
      ),
    );
  }

  // Smart Reminders karta podle mockupů
  Widget _buildSmartRemindersCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: WatercolorTheme.pureWhite.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: WatercolorTheme.deepNavy.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Smart Reminders',
            style: WatercolorTheme.headingSmall.copyWith(
              color: WatercolorTheme.deepNavy,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Perfect day for a beach trip\nSunny, 28°C',
            style: WatercolorTheme.bodySmall.copyWith(
              color: WatercolorTheme.richCharcoal.withValues(alpha: 0.8),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  // Kalendář karta podle mockupů
  Widget _buildCalendarCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: WatercolorTheme.pureWhite.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: WatercolorTheme.deepNavy.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Calendar',
            style: WatercolorTheme.headingSmall.copyWith(
              color: WatercolorTheme.deepNavy,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          // Mini kalendář
          Container(
            height: 80,
            decoration: BoxDecoration(
              color: WatercolorTheme.warmCream.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                DateFormat('MMM\nyyyy').format(DateTime.now()),
                style: WatercolorTheme.bodySmall.copyWith(
                  color: WatercolorTheme.deepNavy,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Navigační tlačítka
  Widget _buildNavigationButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              // Přejít na seznam záznamů
              Navigator.pushNamed(context, '/entries');
            },
            icon: Icon(Icons.list, color: WatercolorTheme.pureWhite),
            label: Text(
              'Záznamy',
              style: TextStyle(color: WatercolorTheme.pureWhite),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: WatercolorTheme.elegantTeal,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              // Přejít na mapu
              Navigator.pushNamed(context, '/map');
            },
            icon: Icon(Icons.map, color: WatercolorTheme.pureWhite),
            label: Text(
              'Mapa',
              style: TextStyle(color: WatercolorTheme.pureWhite),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: WatercolorTheme.sunsetOrange,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Luxusní hlavní obrazovka inspirovaná reference obrázky
  Widget _buildMainDiaryView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Header s názvem podle reference obrázku
          Container(
            padding: const EdgeInsets.fromLTRB(24, 40, 24, 30),
            child: Column(
              children: [
                Text(
                  'ADRIATIC DIARY',
                  style: WatercolorTheme.headingLarge.copyWith(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: WatercolorTheme.deepNavy,
                    letterSpacing: 2.0,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Personal Travel Diary for Croatia',
                  style: WatercolorTheme.bodyLarge.copyWith(
                    fontSize: 16,
                    color: WatercolorTheme.elegantTeal,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Hlavní telefon mockup podle reference obrázku
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            child: LuxuryWatercolorCard(
              style: WatercolorStyle.elegant,
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Telefon s obsahem podle reference
                  Container(
                    width: 280,
                    height: 500,
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(35),
                      boxShadow: [
                        BoxShadow(
                          color: WatercolorTheme.deepNavy.withValues(
                            alpha: 0.2,
                          ),
                          blurRadius: 25,
                          offset: const Offset(0, 12),
                        ),
                      ],
                    ),
                    child: Container(
                      margin: const EdgeInsets.all(3),
                      decoration: BoxDecoration(
                        color: WatercolorTheme.pureWhite,
                        borderRadius: BorderRadius.circular(32),
                      ),
                      child: _buildLuxuryPhoneContent(),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Text podle reference obrázku
                  Text(
                    'Interactive travel calendar\nMap with geotagged memories',
                    style: WatercolorTheme.bodyMedium.copyWith(
                      color: WatercolorTheme.elegantTeal,
                      height: 1.6,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 32),

          // Boční karty podle reference obrázku
          _buildSideCards(),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  // Luxusní obsah telefonu podle reference obrázku
  Widget _buildLuxuryPhoneContent() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(32),
      child: Column(
        children: [
          // Status bar simulace
          Container(
            height: 30,
            color: WatercolorTheme.pureWhite,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Padding(
                  padding: EdgeInsets.only(left: 20),
                  child: Text(
                    '9:41',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: Row(
                    children: [
                      Icon(
                        Icons.signal_cellular_4_bar,
                        size: 16,
                        color: Colors.black,
                      ),
                      const SizedBox(width: 4),
                      Icon(Icons.wifi, size: 16, color: Colors.black),
                      const SizedBox(width: 4),
                      Icon(Icons.battery_full, size: 16, color: Colors.black),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Hlavní obsah telefonu - přesně podle reference obrázku
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    WatercolorTheme.warmCream,
                    WatercolorTheme.softPeach.withValues(alpha: 0.3),
                    WatercolorTheme.coastalMist.withValues(alpha: 0.2),
                  ],
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Nadpis podle reference obrázku
                    Text(
                      'Deníkový zápis',
                      style: WatercolorTheme.headingMedium.copyWith(
                        fontSize: 20,
                        color: WatercolorTheme.deepNavy,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 20),

                    // Mikrofon ikona podle reference
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: WatercolorTheme.sunsetOrange,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: WatercolorTheme.sunsetOrange.withValues(
                              alpha: 0.3,
                            ),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.mic,
                        color: WatercolorTheme.pureWhite,
                        size: 28,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // "Naslouchá..." text podle reference
                    Text(
                      'Naslouchá...',
                      style: WatercolorTheme.bodyMedium.copyWith(
                        color: WatercolorTheme.richCharcoal.withValues(
                          alpha: 0.7,
                        ),
                        fontStyle: FontStyle.italic,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Text deníkového záznamu podle reference
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: WatercolorTheme.pureWhite.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: WatercolorTheme.elegantTeal.withValues(
                            alpha: 0.2,
                          ),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'Dnes jsme jeli trajektem na ostrov Hvar a měli jsme úžasný den na pláži.',
                        style: WatercolorTheme.bodyMedium.copyWith(
                          color: WatercolorTheme.richCharcoal,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 20),

                    // "Napište shrnutí dne" podle reference
                    Text(
                      'Napište shrnutí dne',
                      style: WatercolorTheme.bodyMedium.copyWith(
                        color: WatercolorTheme.deepNavy,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Tagy nálady podle reference obrázku
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildMoodTag('Šťastný', WatercolorTheme.sunsetOrange),
                        const SizedBox(width: 8),
                        _buildMoodTag('Veselý', WatercolorTheme.luxuryGold),
                        const SizedBox(width: 8),
                        _buildMoodTag('Pokojný', WatercolorTheme.elegantTeal),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Boční karty podle reference obrázku
  Widget _buildSideCards() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        children: [
          // Smart Reminders karta
          Expanded(
            child: LuxuryWatercolorCard(
              style: WatercolorStyle.coastal,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.notifications_active,
                    color: WatercolorTheme.elegantTeal,
                    size: 32,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Smart Reminders',
                    style: WatercolorTheme.headingSmall.copyWith(
                      fontSize: 16,
                      color: WatercolorTheme.deepNavy,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'AI-powered daily prompts for your travel memories',
                    style: WatercolorTheme.bodySmall.copyWith(
                      color: WatercolorTheme.richCharcoal,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Calendar karta
          Expanded(
            child: LuxuryWatercolorCard(
              style: WatercolorStyle.luxury,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: WatercolorTheme.luxuryGold,
                    size: 32,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Calendar',
                    style: WatercolorTheme.headingSmall.copyWith(
                      fontSize: 16,
                      color: WatercolorTheme.deepNavy,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Track your journey through Croatia with beautiful calendar views',
                    style: WatercolorTheme.bodySmall.copyWith(
                      color: WatercolorTheme.richCharcoal,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Video deník tab
  Widget _buildVideoView() {
    return VideoDiaryWidget(
      videos: _videos,
      onVideoTap: (video) {
        // Přehrát video
        _playVideo(video);
      },
      onVideoEdit: (video) {
        // Upravit video
        _editVideo(video);
      },
      onVideoDelete: (video) {
        // Smazat video
        _deleteVideo(video);
      },
    );
  }

  // Starý seznam záznamů (nyní druhý tab)
  Widget _buildEntriesListView() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_filteredEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.book_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Žádné záznamy',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Začněte psát svůj cestovatelský deník',
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createNewEntry,
              icon: const Icon(Icons.add),
              label: const Text('Vytvořit první záznam'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Vyhledávací pole
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'Hledat v deníku...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterEntries();
            },
          ),
        ),

        // Seznam záznamů
        Expanded(
          child: ListView.builder(
            itemCount: _filteredEntries.length,
            itemBuilder: (context, index) {
              final entry = _filteredEntries[index];
              return _buildEntryCard(entry);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEntryCard(DiaryEntry entry) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _openEntryDetail(entry),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hlavička s datem a náladou
              Row(
                children: [
                  Text(
                    DateFormat('dd.MM.yyyy').format(entry.date),
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  if (entry.mood != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      entry.mood!.emoji,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                  const Spacer(),
                  if (entry.rating != null)
                    Row(
                      children: [
                        const Icon(Icons.star, size: 16, color: Colors.amber),
                        Text(
                          entry.rating!.toStringAsFixed(1),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                ],
              ),

              const SizedBox(height: 8),

              // Titulek
              Text(
                entry.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 8),

              // Lokace
              if (entry.location != null)
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        entry.location!,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ),
                  ],
                ),

              const SizedBox(height: 8),

              // Excerpt obsahu
              Text(
                entry.excerpt,
                style: const TextStyle(fontSize: 14),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Indikátory médií a tagy
              Row(
                children: [
                  if (entry.hasPhotos)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.photo, size: 14, color: Colors.blue),
                          const SizedBox(width: 4),
                          Text(
                            '${entry.photos.length}',
                            style: const TextStyle(
                              color: Colors.blue,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),

                  if (entry.hasVoiceNotes) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.mic, size: 14, color: Colors.green),
                          const SizedBox(width: 4),
                          Text(
                            '${entry.voiceNotes.length}',
                            style: const TextStyle(
                              color: Colors.green,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  const Spacer(),

                  // Počet slov
                  Text(
                    '${entry.wordCount} slov',
                    style: TextStyle(color: Colors.grey[500], fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarView() {
    return Container(
      color: const Color(0xFFF8F6F0), // Krémový papír pozadí
      child: Stack(
        children: [
          // Watercolor mapa Chorvatska v pozadí
          Positioned(right: -50, top: 50, child: _buildWatercolorCroatiaMap()),

          Center(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Telefon mockup podle obrázku
                  Container(
                    width: 300,
                    height: 500,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // Header kalendáře
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              IconButton(
                                onPressed: () => _changeMonth(-1),
                                icon: const Icon(
                                  Icons.chevron_left,
                                  color: Color(0xFF006994),
                                ),
                              ),
                              Text(
                                DateFormat('MMMM yyyy').format(_currentMonth),
                                style: GoogleFonts.inter(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF006994),
                                ),
                              ),
                              IconButton(
                                onPressed: () => _changeMonth(1),
                                icon: const Icon(
                                  Icons.chevron_right,
                                  color: Color(0xFF006994),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Dny v týdnu
                          Row(
                            children: ['M', 'T', 'W', 'T', 'F', 'S', 'S']
                                .map(
                                  (day) => Expanded(
                                    child: Center(
                                      child: Text(
                                        day,
                                        style: GoogleFonts.inter(
                                          color: const Color(0xFF666666),
                                          fontWeight: FontWeight.w500,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),

                          const SizedBox(height: 12),

                          // Kalendářová mřížka podle obrázku
                          Expanded(child: _buildPhoneMockupCalendar()),

                          const SizedBox(height: 20),

                          // Text podle obrázku
                          Text(
                            'Interactive travel calendar',
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              color: const Color(0xFF666666),
                            ),
                          ),

                          const SizedBox(height: 8),

                          Text(
                            'Map with geotagged memories',
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF2E8B8B),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsView() {
    final totalEntries = _entries.length;
    final totalWords = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.wordCount,
    );
    final totalPhotos = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.photos.length,
    );
    final totalVoiceNotes = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.voiceNotes.length,
    );

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Statistiky deníku',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Základní statistiky
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Záznamy',
                  totalEntries.toString(),
                  Icons.book,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Slova',
                  totalWords.toString(),
                  Icons.text_fields,
                  Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Fotografie',
                  totalPhotos.toString(),
                  Icons.photo,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Hlasové poznámky',
                  totalVoiceNotes.toString(),
                  Icons.mic,
                  Colors.purple,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Nálady
          const Text(
            'Nálady',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          ..._buildMoodStatistics(),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMoodStatistics() {
    final moodCounts = <DiaryMood, int>{};
    for (final entry in _entries) {
      if (entry.mood != null) {
        moodCounts[entry.mood!] = (moodCounts[entry.mood!] ?? 0) + 1;
      }
    }

    return moodCounts.entries.map((entry) {
      final percentage = _entries.isNotEmpty
          ? (entry.value / _entries.length * 100).round()
          : 0;

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Text(entry.key.emoji, style: const TextStyle(fontSize: 20)),
            const SizedBox(width: 8),
            Expanded(child: Text(entry.key.displayName)),
            Text('${entry.value}x ($percentage%)'),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildTemplatesView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Šablony pro deník',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: DiaryTemplates.templates.length,
              itemBuilder: (context, index) {
                final template = DiaryTemplates.templates[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  child: ListTile(
                    leading: const Icon(Icons.description),
                    title: Text(template['title'] as String),
                    subtitle: Text(
                      (template['content'] as String).split('\n').first,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _useTemplate(template),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _createNewEntry() {
    // Navigace na obrazovku pro vytvoření nového záznamu
    Navigator.of(context)
        .push(
          MaterialPageRoute(builder: (context) => const DiaryEntryEditScreen()),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries(); // Obnovit seznam po vytvoření
          }
        });
  }

  void _openEntryDetail(DiaryEntry entry) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => DiaryEntryDetailScreen(entry: entry),
          ),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries(); // Obnovit seznam po úpravě
          }
        });
  }

  void _useTemplate(Map<String, dynamic> template) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => DiaryEntryEditScreen(template: template),
          ),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries();
          }
        });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrovat záznamy'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Filtr podle nálady
            DropdownButtonFormField<DiaryMood?>(
              value: _selectedMood,
              decoration: const InputDecoration(labelText: 'Nálada'),
              items: [
                const DropdownMenuItem<DiaryMood?>(
                  value: null,
                  child: Text('Všechny nálady'),
                ),
                ...DiaryMood.values.map(
                  (mood) => DropdownMenuItem(
                    value: mood,
                    child: Text('${mood.emoji} ${mood.displayName}'),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedMood = value;
                });
              },
            ),

            const SizedBox(height: 16),

            // Filtr podle data
            ListTile(
              title: Text(
                _selectedDate == null
                    ? 'Vybrat datum'
                    : DateFormat('dd.MM.yyyy').format(_selectedDate!),
              ),
              leading: const Icon(Icons.calendar_today),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _selectedDate ?? DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _selectedDate = date;
                  });
                }
              },
            ),

            if (_selectedDate != null)
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedDate = null;
                  });
                },
                child: const Text('Zrušit filtr data'),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedMood = null;
                _selectedDate = null;
              });
              _filterEntries();
              Navigator.of(context).pop();
            },
            child: const Text('Zrušit filtry'),
          ),
          ElevatedButton(
            onPressed: () {
              _filterEntries();
              Navigator.of(context).pop();
            },
            child: const Text('Použít'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export deníku'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.picture_as_pdf),
              title: Text('Export do PDF'),
              subtitle: Text('Vytvoří PDF soubor s vašimi záznamy'),
            ),
            ListTile(
              leading: Icon(Icons.code),
              title: Text('Export do HTML'),
              subtitle: Text('Vytvoří webovou stránku s deníkem'),
            ),
            ListTile(
              leading: Icon(Icons.share),
              title: Text('Sdílet záznamy'),
              subtitle: Text('Sdílet vybrané záznamy na sociálních sítích'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportToPdf();
            },
            child: const Text('Export PDF'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToPdf() async {
    try {
      final filePath = await _exportService.exportDiaryToPdf(
        entries: _entries,
        title: 'Můj cestovatelský deník',
        includePhotos: true,
        includeVoiceNotes: false,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deník exportován: $filePath'),
            action: SnackBarAction(
              label: 'Otevřít',
              onPressed: () {
                // Otevřít soubor
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při exportu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Kalendářové metody
  void _changeMonth(int direction) {
    setState(() {
      _currentMonth = DateTime(
        _currentMonth.year,
        _currentMonth.month + direction,
        1,
      );
    });
  }

  Widget _buildPhoneMockupCalendar() {
    final firstDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month,
      1,
    );
    final lastDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month + 1,
      0,
    );
    final firstDayWeekday = firstDayOfMonth.weekday;
    final daysInMonth = lastDayOfMonth.day;

    // Předchozí měsíc pro vyplnění
    final previousMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month - 1,
      0,
    );
    final daysFromPreviousMonth = firstDayWeekday - 1;

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: 42, // 6 týdnů × 7 dní
      itemBuilder: (context, index) {
        DateTime cellDate;
        bool isCurrentMonth = true;
        bool isToday = false;

        if (index < daysFromPreviousMonth) {
          // Dny z předchozího měsíce
          cellDate = DateTime(
            previousMonth.year,
            previousMonth.month,
            previousMonth.day - (daysFromPreviousMonth - index - 1),
          );
          isCurrentMonth = false;
        } else if (index < daysFromPreviousMonth + daysInMonth) {
          // Dny aktuálního měsíce
          cellDate = DateTime(
            _currentMonth.year,
            _currentMonth.month,
            index - daysFromPreviousMonth + 1,
          );
          final today = DateTime.now();
          isToday =
              cellDate.year == today.year &&
              cellDate.month == today.month &&
              cellDate.day == today.day;
        } else {
          // Dny z následujícího měsíce
          cellDate = DateTime(
            _currentMonth.year,
            _currentMonth.month + 1,
            index - daysFromPreviousMonth - daysInMonth + 1,
          );
          isCurrentMonth = false;
        }

        // Zkontroluj, jestli má tento den záznam (simulace podle obrázku)
        final hasMemory =
            isCurrentMonth && [5, 12, 18, 25].contains(cellDate.day);

        return _buildPhoneMockupDay(
          cellDate,
          isCurrentMonth,
          isToday,
          hasMemory,
        );
      },
    );
  }

  Widget _buildPhoneMockupDay(
    DateTime date,
    bool isCurrentMonth,
    bool isToday,
    bool hasMemory,
  ) {
    Color backgroundColor = Colors.transparent;
    Color textColor = isCurrentMonth
        ? const Color(0xFF2C2C2C)
        : const Color(0xFF2C2C2C).withValues(alpha: 0.3);

    if (hasMemory) {
      backgroundColor = const Color(
        0xFFFF6B35,
      ).withValues(alpha: 0.1); // Jemné oranžové pozadí
    }

    return GestureDetector(
      onTap: isCurrentMonth ? () => _showDayEntries(date) : null,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: hasMemory && isCurrentMonth
              ? const Text(
                  '😊', // Emoji podle obrázku
                  style: TextStyle(fontSize: 16),
                )
              : Text(
                  date.day.toString(),
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: textColor,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month,
      1,
    );
    final lastDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month + 1,
      0,
    );
    final firstDayWeekday = firstDayOfMonth.weekday;
    final daysInMonth = lastDayOfMonth.day;

    // Předchozí měsíc pro vyplnění
    final previousMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month - 1,
      0,
    );
    final daysFromPreviousMonth = firstDayWeekday - 1;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: 42, // 6 týdnů × 7 dní
        itemBuilder: (context, index) {
          DateTime cellDate;
          bool isCurrentMonth = true;
          bool isToday = false;

          if (index < daysFromPreviousMonth) {
            // Dny z předchozího měsíce
            cellDate = DateTime(
              previousMonth.year,
              previousMonth.month,
              previousMonth.day - (daysFromPreviousMonth - index - 1),
            );
            isCurrentMonth = false;
          } else if (index < daysFromPreviousMonth + daysInMonth) {
            // Dny aktuálního měsíce
            cellDate = DateTime(
              _currentMonth.year,
              _currentMonth.month,
              index - daysFromPreviousMonth + 1,
            );
            final today = DateTime.now();
            isToday =
                cellDate.year == today.year &&
                cellDate.month == today.month &&
                cellDate.day == today.day;
          } else {
            // Dny z následujícího měsíce
            cellDate = DateTime(
              _currentMonth.year,
              _currentMonth.month + 1,
              index - daysFromPreviousMonth - daysInMonth + 1,
            );
            isCurrentMonth = false;
          }

          // Zkontroluj, jestli má tento den záznam
          final hasEntry = _entries.any(
            (entry) =>
                entry.date.year == cellDate.year &&
                entry.date.month == cellDate.month &&
                entry.date.day == cellDate.day,
          );

          final isSelected =
              _selectedCalendarDate != null &&
              _selectedCalendarDate!.year == cellDate.year &&
              _selectedCalendarDate!.month == cellDate.month &&
              _selectedCalendarDate!.day == cellDate.day;

          return _buildCalendarDay(
            cellDate,
            isCurrentMonth,
            isToday,
            hasEntry,
            isSelected,
          );
        },
      ),
    );
  }

  Widget _buildCalendarDay(
    DateTime date,
    bool isCurrentMonth,
    bool isToday,
    bool hasEntry,
    bool isSelected,
  ) {
    Color backgroundColor = Colors.transparent;
    Color textColor = isCurrentMonth
        ? const Color(0xFF2C2C2C)
        : const Color(0xFF2C2C2C).withValues(alpha: 0.3);

    if (isToday) {
      backgroundColor = const Color(0xFF006994); // Jaderská modrá
      textColor = Colors.white;
    } else if (isSelected) {
      backgroundColor = const Color(0xFF2E8B8B); // Středomořská tyrkysová
      textColor = Colors.white;
    } else if (hasEntry) {
      backgroundColor = const Color(
        0xFFFF6B35,
      ).withValues(alpha: 0.2); // Oranžová západ slunce
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCalendarDate = date;
        });
        _showDayEntries(date);
      },
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: hasEntry && !isToday && !isSelected
              ? Border.all(color: const Color(0xFFFF6B35), width: 2)
              : null,
        ),
        child: Stack(
          children: [
            Center(
              child: Text(
                date.day.toString(),
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: isToday ? FontWeight.bold : FontWeight.w500,
                  color: textColor,
                ),
              ),
            ),
            if (hasEntry)
              Positioned(
                top: 4,
                right: 4,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: isToday || isSelected
                        ? Colors.white
                        : const Color(0xFFFF6B35),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showDayEntries(DateTime date) {
    final dayEntries = _entries
        .where(
          (entry) =>
              entry.date.year == date.year &&
              entry.date.month == date.month &&
              entry.date.day == date.day,
        )
        .toList();

    if (dayEntries.isEmpty) {
      // Zobrazit možnost vytvoření nového záznamu
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            DateFormat('d. MMMM yyyy').format(date),
            style: GoogleFonts.playfairDisplay(fontWeight: FontWeight.bold),
          ),
          content: const Text('Pro tento den nemáte žádné záznamy.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Zrušit'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _createNewEntryForDate(date);
              },
              child: const Text('Vytvořit záznam'),
            ),
          ],
        ),
      );
    } else {
      // Zobrazit záznamy pro daný den
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => _buildDayEntriesSheet(date, dayEntries),
      );
    }
  }

  Widget _buildDayEntriesSheet(DateTime date, List<DiaryEntry> entries) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Color(0xFFF8F6F0), // Krémový papír
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    DateFormat('d. MMMM yyyy').format(date),
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF006994),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _createNewEntryForDate(date),
                  icon: const Icon(Icons.add, color: Color(0xFF006994)),
                ),
              ],
            ),
          ),

          // Záznamy
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: entries.length,
              itemBuilder: (context, index) => _buildEntryCard(entries[index]),
            ),
          ),
        ],
      ),
    );
  }

  void _createNewEntryForDate(DateTime date) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DiaryEntryEditScreen(template: {'date': date}),
      ),
    );
  }

  // Video metody
  void _playVideo(VideoDiary video) {
    // Implementace přehrávání videa
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(video.title),
        content: Text('Přehrávání videa: ${video.formattedDuration}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  void _editVideo(VideoDiary video) {
    // Implementace editace videa
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upravit video'),
        content: Text('Editace videa: ${video.title}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Zde by byla implementace editace
            },
            child: const Text('Upravit'),
          ),
        ],
      ),
    );
  }

  void _deleteVideo(VideoDiary video) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Smazat video'),
        content: Text('Opravdu chcete smazat video "${video.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _videos.removeWhere((v) => v.id == video.id);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Video bylo smazáno')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Smazat'),
          ),
        ],
      ),
    );
  }

  // Watercolor pozadí Chorvatska podle obrázku
  Widget _buildWatercolorBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: const Alignment(0.3, -0.2),
          radius: 1.5,
          colors: [
            const Color(0xFF006994).withValues(alpha: 0.3), // Adriatic blue
            const Color(
              0xFF2E8B8B,
            ).withValues(alpha: 0.2), // Mediterranean turquoise
            const Color(0xFF006994).withValues(alpha: 0.1),
            Colors.transparent,
          ],
          stops: const [0.0, 0.4, 0.7, 1.0],
        ),
      ),
      child: CustomPaint(painter: WatercolorPainter(), size: Size.infinite),
    );
  }



  // Tlačítko v telefonu podle obrázku s watercolor efektem
  Widget _buildPhoneButton(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: CustomPaint(
        painter: WatercolorButtonPainter(color),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Text(
            text,
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ),
      ),
    );
  }

  // Watercolor mapa Chorvatska pro kalendář
  Widget _buildWatercolorCroatiaMap() {
    return Container(
      width: 200,
      height: 300,
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: const Alignment(-0.3, 0.2),
          radius: 1.0,
          colors: [
            const Color(0xFF006994).withValues(alpha: 0.2), // Adriatic blue
            const Color(
              0xFF2E8B8B,
            ).withValues(alpha: 0.15), // Mediterranean turquoise
            Colors.transparent,
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
      child: CustomPaint(
        painter: CroatiaMapPainter(),
        size: const Size(200, 300),
      ),
    );
  }

  // Akční menu s novými funkcemi
  void _showActionMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFFF8F6F0),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Text(
                    'Nový obsah',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF006994),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Nový záznam
                  _buildActionTile(
                    icon: Icons.edit,
                    title: 'Nový záznam',
                    subtitle: 'Napište nový deníkový záznam',
                    onTap: () {
                      Navigator.pop(context);
                      _createNewEntry();
                    },
                  ),

                  const SizedBox(height: 16),

                  // Video deník
                  _buildActionTile(
                    icon: Icons.videocam,
                    title: 'Video deník',
                    subtitle: 'Natočte nebo importujte video',
                    onTap: () {
                      Navigator.pop(context);
                      _showVideoOptions();
                    },
                  ),

                  const SizedBox(height: 16),

                  // Print services
                  _buildActionTile(
                    icon: Icons.print,
                    title: 'Tiskové služby',
                    subtitle: 'Vytiskněte fotoknihu nebo kalendář',
                    onTap: () {
                      Navigator.pop(context);
                      _showPrintOptions();
                    },
                  ),

                  const SizedBox(height: 16),

                  // Chorvatská kultura
                  _buildActionTile(
                    icon: Icons.language,
                    title: 'Chorvatská kultura',
                    subtitle: 'Objevte místní tradice a jazyk',
                    onTap: () {
                      Navigator.pop(context);
                      _showCulturalFeatures();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: const Color(0xFF006994).withValues(alpha: 0.2),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF006994).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: const Color(0xFF006994)),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2C2C2C),
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFF666666),
            ),
          ],
        ),
      ),
    );
  }

  void _showVideoOptions() {
    // Implementace video možností
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Video deník'),
        content: const Text('Funkce video deníku bude brzy dostupná!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showPrintOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFFF8F6F0),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Text(
                    'Tiskové služby',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF006994),
                    ),
                  ),
                  const SizedBox(height: 24),

                  _buildActionTile(
                    icon: Icons.book,
                    title: 'Fotokniha',
                    subtitle: 'Vytiskněte profesionální fotoknihu',
                    onTap: () {
                      Navigator.pop(context);
                      _createPhotoBook();
                    },
                  ),

                  const SizedBox(height: 16),

                  _buildActionTile(
                    icon: Icons.calendar_month,
                    title: 'Kalendář',
                    subtitle: 'Vytvořte kalendář s vašimi vzpomínkami',
                    onTap: () {
                      Navigator.pop(context);
                      _createCalendar();
                    },
                  ),

                  const SizedBox(height: 16),

                  _buildActionTile(
                    icon: Icons.photo_library,
                    title: 'Fotografie',
                    subtitle: 'Vytiskněte jednotlivé fotografie',
                    onTap: () {
                      Navigator.pop(context);
                      _printPhotos();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCulturalFeatures() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFFF8F6F0),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Text(
                    'Chorvatská kultura',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF006994),
                    ),
                  ),
                  const SizedBox(height: 24),

                  _buildActionTile(
                    icon: Icons.restaurant,
                    title: 'Tradiční recepty',
                    subtitle: 'Objevte chorvatskou kuchyni',
                    onTap: () {
                      Navigator.pop(context);
                      _showTraditionalRecipes();
                    },
                  ),

                  const SizedBox(height: 16),

                  _buildActionTile(
                    icon: Icons.school,
                    title: 'Učte se chorvatsky',
                    subtitle: 'Denní lekce chorvatštiny',
                    onTap: () {
                      Navigator.pop(context);
                      _showLanguageLearning();
                    },
                  ),

                  const SizedBox(height: 16),

                  _buildActionTile(
                    icon: Icons.history_edu,
                    title: 'Historie a tradice',
                    subtitle: 'Poznejte místní historii',
                    onTap: () {
                      Navigator.pop(context);
                      _showHistoricalContext();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Print service metody
  Future<void> _createPhotoBook() async {
    if (_entries.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Nejprve vytvořte nějaké záznamy')),
      );
      return;
    }

    final order = await _printService.createPhotoBook(
      entries: _entries,
      template: PhotoBookTemplate.travel,
      size: PhotoBookSize.medium,
      title: 'Můj chorvatský deník',
      subtitle: 'Vzpomínky na cestování',
    );

    if (order != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Fotokniha byla vytvořena! ID: ${order.id}')),
      );
    }
  }

  Future<void> _createCalendar() async {
    final order = await _printService.createCalendar(
      entries: _entries,
      year: DateTime.now().year,
      type: CalendarType.wall,
      size: CalendarSize.a4,
    );

    if (order != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Kalendář byl vytvořen! ID: ${order.id}')),
      );
    }
  }

  Future<void> _printPhotos() async {
    final allPhotos = _entries.expand((entry) => entry.photos).toList();

    if (allPhotos.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Nejprve přidejte nějaké fotografie')),
      );
      return;
    }

    final order = await _printService.printPhotos(
      photoPaths: allPhotos,
      size: PhotoSize.medium,
      paper: PhotoPaper.glossy,
    );

    if (order != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fotografie připraveny k tisku! ID: ${order.id}'),
        ),
      );
    }
  }

  // Kulturní funkce
  Future<void> _showTraditionalRecipes() async {
    final recipes = await _cultureService.getTraditionalRecipes();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tradiční recepty'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: recipes.length,
            itemBuilder: (context, index) {
              final recipe = recipes[index];
              return ListTile(
                title: Text(recipe.name),
                subtitle: Text(recipe.description),
                trailing: Text(recipe.region),
                onTap: () {
                  // Zobrazit detail receptu
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  Future<void> _showLanguageLearning() async {
    final lesson = await _cultureService.getDailyLesson();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(lesson.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Úroveň: ${lesson.level.name}'),
            const SizedBox(height: 16),
            const Text('Fráze:', style: TextStyle(fontWeight: FontWeight.bold)),
            ...lesson.phrases.map((phrase) => Text('• $phrase')),
            const SizedBox(height: 16),
            Text('Tip: ${lesson.culturalTip}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  Future<void> _showHistoricalContext() async {
    // Simulace získání historického kontextu pro aktuální lokaci
    final context = await _cultureService.getHistoricalContext(
      latitude: 45.8150, // Zagreb
      longitude: 15.9819,
      placeName: 'Zagreb',
    );

    if (context != null) {
      showDialog(
        context: this.context,
        builder: (ctx) => AlertDialog(
          title: Text(context.site.name),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(context.culturalSignificance),
              const SizedBox(height: 16),
              const Text(
                'Klíčové události:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              ...context.keyEvents.map((event) => Text('• $event')),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(ctx),
              child: const Text('Zavřít'),
            ),
          ],
        ),
      );
    } else {
      ScaffoldMessenger.of(this.context).showSnackBar(
        const SnackBar(
          content: Text('Pro tuto lokaci nejsou dostupné historické informace'),
        ),
      );
    }
  }
}

// Watercolor painter pro pozadí
class WatercolorPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    // Kreslení watercolor efektu - ostrovy a moře
    final path1 = Path();
    path1.moveTo(size.width * 0.2, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.2,
      size.width * 0.6,
      size.height * 0.35,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.5,
      size.width * 0.9,
      size.height * 0.7,
    );
    path1.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.8,
      size.width * 0.3,
      size.height * 0.75,
    );
    path1.quadraticBezierTo(
      size.width * 0.1,
      size.height * 0.6,
      size.width * 0.2,
      size.height * 0.3,
    );

    paint.color = const Color(0xFF006994).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhý ostrov
    final path2 = Path();
    path2.moveTo(size.width * 0.6, size.height * 0.1);
    path2.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.05,
      size.width * 0.95,
      size.height * 0.2,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.4,
      size.width * 0.7,
      size.height * 0.3,
    );
    path2.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.2,
      size.width * 0.6,
      size.height * 0.1,
    );

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.25);
    canvas.drawPath(path2, paint);

    // Třetí ostrov
    final path3 = Path();
    path3.moveTo(size.width * 0.1, size.height * 0.8);
    path3.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.85,
      size.width * 0.4,
      size.height * 0.9,
    );
    path3.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.95,
      size.width * 0.05,
      size.height * 0.9,
    );
    path3.quadraticBezierTo(
      size.width * 0.08,
      size.height * 0.85,
      size.width * 0.1,
      size.height * 0.8,
    );

    paint.color = const Color(0xFF006994).withValues(alpha: 0.15);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Croatia map painter pro kalendář
class CroatiaMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 1.5;

    // Kreslení zjednodušené mapy Chorvatska
    final path = Path();

    // Istrie (severní část)
    path.moveTo(size.width * 0.2, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.05,
      size.width * 0.5,
      size.height * 0.15,
    );
    path.lineTo(size.width * 0.45, size.height * 0.25);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.2,
      size.height * 0.1,
    );

    paint.color = const Color(0xFF006994).withValues(alpha: 0.3);
    canvas.drawPath(path, paint);

    // Dalmatské pobřeží
    final coastPath = Path();
    coastPath.moveTo(size.width * 0.3, size.height * 0.3);
    coastPath.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.5,
      size.width * 0.35,
      size.height * 0.7,
    );
    coastPath.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.85,
      size.width * 0.5,
      size.height * 0.9,
    );
    coastPath.lineTo(size.width * 0.55, size.height * 0.85);
    coastPath.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.6,
      size.width * 0.55,
      size.height * 0.4,
    );
    coastPath.quadraticBezierTo(
      size.width * 0.45,
      size.height * 0.25,
      size.width * 0.3,
      size.height * 0.3,
    );

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.25);
    canvas.drawPath(coastPath, paint);

    // Ostrovy
    final islandPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = const Color(0xFF006994).withValues(alpha: 0.4);

    // Hvar
    canvas.drawCircle(
      Offset(size.width * 0.15, size.height * 0.6),
      3,
      islandPaint,
    );

    // Korčula
    canvas.drawCircle(
      Offset(size.width * 0.1, size.height * 0.75),
      2.5,
      islandPaint,
    );

    // Brač
    canvas.drawCircle(
      Offset(size.width * 0.2, size.height * 0.55),
      2,
      islandPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Placeholder obrazovky pro navigaci
class DiaryEntryEditScreen extends StatelessWidget {
  final Map<String, dynamic>? template;

  const DiaryEntryEditScreen({super.key, this.template});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Nový záznam')),
      body: const Center(child: Text('Obrazovka pro editaci záznamu')),
    );
  }
}

class DiaryEntryDetailScreen extends StatelessWidget {
  final DiaryEntry entry;

  const DiaryEntryDetailScreen({super.key, required this.entry});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(entry.title)),
      body: const Center(child: Text('Detail záznamu')),
    );
  }
}

// Watercolor painter pro header
class WatercolorHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    // Watercolor efekt - více vrstev s různou průhledností
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.1,
      size.width * 0.6,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.6,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.3);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.8,
      size.width,
      size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.2);
    canvas.drawPath(path2, paint);

    // Třetí vrstva - nejjemnější
    final path3 = Path();
    path3.moveTo(0, size.height * 0.7);
    path3.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.4,
      size.width * 0.5,
      size.height * 0.8,
    );
    path3.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.9,
      size.width,
      size.height * 0.6,
    );
    path3.lineTo(size.width, size.height);
    path3.lineTo(0, size.height);
    path3.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.15);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Watercolor painter pro tlačítka
class WatercolorButtonPainter extends CustomPainter {
  final Color color;

  WatercolorButtonPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Hlavní watercolor tvar
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.6,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.85,
      size.height * 0.25,
      size.width * 0.9,
      size.height * 0.5,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.75,
      size.width * 0.7,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.95,
      size.width * 0.1,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.5,
      size.width * 0.1,
      size.height * 0.2,
    );
    path.close();

    // Základní watercolor vrstva
    paint.color = color.withValues(alpha: 0.2);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.2, size.height * 0.3);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.1,
      size.width * 0.8,
      size.height * 0.4,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.7,
      size.width * 0.6,
      size.height * 0.9,
    );
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.8,
      size.width * 0.2,
      size.height * 0.3,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Watercolor painter pro floating action button
class WatercolorFabPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Watercolor gradient efekt
    final gradient = RadialGradient(
      center: Alignment.center,
      radius: 1.0,
      colors: [
        const Color(0xFFFF6B35).withValues(alpha: 0.9),
        const Color(0xFFFF6B35).withValues(alpha: 0.7),
        const Color(0xFF2E8B8B).withValues(alpha: 0.5),
        const Color(0xFF006994).withValues(alpha: 0.3),
      ],
      stops: const [0.0, 0.4, 0.7, 1.0],
    );

    paint.shader = gradient.createShader(
      Rect.fromCircle(center: center, radius: radius),
    );

    // Hlavní kruh
    canvas.drawCircle(center, radius, paint);

    // Watercolor okraje - nepravidelné
    final path = Path();
    for (int i = 0; i < 360; i += 10) {
      final angle = i * 3.14159 / 180;
      final variation = (i % 30 == 0) ? 0.9 : 1.0; // Nepravidelnost
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.shader = null;
    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.2);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
