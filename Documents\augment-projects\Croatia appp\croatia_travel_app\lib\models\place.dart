import 'package:json_annotation/json_annotation.dart';

part 'place.g.dart';

@JsonSerializable()
class Place {
  final String id;
  final String name;
  final String description;
  final String? excerpt; // Krátký popis pro karty
  final double latitude;
  final double longitude;
  final String region;
  final PlaceType type;
  final List<String> images;
  final bool isVisited;
  final DateTime? visitedDate;
  final String? userNotes;
  final double? rating;
  final List<String> tags;
  final Map<String, dynamic>? additionalInfo;

  Place({
    required this.id,
    required this.name,
    required this.description,
    this.excerpt,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.type,
    this.images = const [],
    this.isVisited = false,
    this.visitedDate,
    this.userNotes,
    this.rating,
    this.tags = const [],
    this.additionalInfo,
  });

  factory Place.fromJson(Map<String, dynamic> json) => _$PlaceFromJson(json);
  Map<String, dynamic> toJson() => _$PlaceToJson(this);

  Place copyWith({
    String? id,
    String? name,
    String? description,
    String? excerpt,
    double? latitude,
    double? longitude,
    String? region,
    PlaceType? type,
    List<String>? images,
    bool? isVisited,
    DateTime? visitedDate,
    String? userNotes,
    double? rating,
    List<String>? tags,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Place(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      region: region ?? this.region,
      type: type ?? this.type,
      images: images ?? this.images,
      isVisited: isVisited ?? this.isVisited,
      visitedDate: visitedDate ?? this.visitedDate,
      userNotes: userNotes ?? this.userNotes,
      rating: rating ?? this.rating,
      tags: tags ?? this.tags,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }
}

enum PlaceType {
  @JsonValue('monument')
  monument,
  @JsonValue('beach')
  beach,
  @JsonValue('restaurant')
  restaurant,
  @JsonValue('hotel')
  hotel,
  @JsonValue('museum')
  museum,
  @JsonValue('park')
  park,
  @JsonValue('church')
  church,
  @JsonValue('castle')
  castle,
  @JsonValue('viewpoint')
  viewpoint,
  @JsonValue('other')
  other,
}
