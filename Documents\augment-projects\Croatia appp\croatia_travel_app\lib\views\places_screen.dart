import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/place.dart';
import '../services/location_service.dart';
import '../widgets/place_card.dart';
import '../widgets/filter_widget.dart';
import '../widgets/skeleton_loading.dart';
import '../widgets/error_state_widget.dart';

class PlacesScreen extends StatefulWidget {
  const PlacesScreen({super.key});

  @override
  State<PlacesScreen> createState() => _PlacesScreenState();
}

class _PlacesScreenState extends State<PlacesScreen> {
  List<Place> _allPlaces = [];
  List<Place> _filteredPlaces = [];
  String _searchQuery = '';
  String _selectedRegion = 'all';
  PlaceType? _selectedType;
  bool _showVisitedOnly = false;
  bool _sortByDistance = false;
  bool _isLoading = true;
  String? _errorMessage;

  final LocationService _locationService = LocationService();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPlaces();
  }

  Future<void> _loadPlaces() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Simulace načítání dat s možnou chybou
      await Future.delayed(const Duration(milliseconds: 800));

      // Simulace 10% šance na chybu pro testování
      if (DateTime.now().millisecond % 10 == 0) {
        throw Exception('Simulovaná chyba sítě');
      }

      // Načtení míst z databáze - zde simulace dat
      _allPlaces = [
        Place(
          id: '1',
          name: 'Dubrovník - Staré město',
          description:
              'Dubrovník, zvaný "Perla Jadranu", je jedním z nejkrásnějších historických měst Evropy. Toto UNESCO světové dědictví ohromuje návštěvníky svými dokonale zachovanými středověkými hradbami, které obklopují staré město jako ochranný prstenec. Procházka po hradbách nabízí úchvatné výhledy na azurové moře a červené střechy města.\n\nStaré město je labyrintem úzkých dlážděných uliček, kde se skrývají gotické, renesanční a barokní paláce, kostely a kláštery. Hlavní ulice Stradun, dlážděná bílým vápencem, vede od Pile Gate k Ploče Gate a je srdcem městského života. Zde najdete kavárny, restaurace a obchody v historických budovách.\n\nDubrovník se stal světově známým díky seriálu Game of Thrones, kde představoval King\'s Landing. Můžete navštívit místa natáčení včetně Jesuit Stairs (Walk of Shame) a Fort Lovrijenac (Red Keep). Město nabízí také bohatý kulturní program s festivaly, koncerty a divadelními představeními v historických kulisách.',
          latitude: 42.6407,
          longitude: 18.1077,
          region: 'dalmatia',
          type: PlaceType.monument,
          images: ['dubrovnik1.jpg', 'dubrovnik2.jpg'],
          isVisited: true,
          visitedDate: DateTime.now().subtract(const Duration(days: 5)),
          rating: 5.0,
          tags: [
            'UNESCO',
            'historie',
            'hradby',
            'Game of Thrones',
            'středověk',
            'architektura',
          ],
          additionalInfo: {
            'opening_hours': 'Hradby: 8:00-19:30 (léto), 9:00-15:00 (zima)',
            'entrance_fee': 'Hradby: 35 EUR dospělí, 15 EUR děti',
            'best_time': 'Duben-červen, září-říjen (méně turistů)',
            'duration': '1-2 dny pro kompletní prohlídku',
            'highlights': 'Hradby, Stradun, Rector\'s Palace, Sponza Palace',
            'tips':
                'Kupte si Dubrovnik Card pro slevy, vyhněte se poledním hodinám v létě',
          },
        ),
        Place(
          id: '2',
          name: 'Plitvická jezera',
          description:
              'Plitvická jezera jsou korunou chorvatské přírody a jedním z nejstarších národních parků v jihovýchodní Evropě. Tento UNESCO světový přírodní park se rozkládá na ploše 296 km² a skládá se ze 16 terasovitě uspořádaných jezer propojených 92 vodopády a kaskádami.\n\nJezera jsou rozdělena na Horní jezera (Gornja jezera) a Dolní jezera (Donja jezera). Horní jezera jsou obklopena hustými lesy a nabízejí klidnější atmosféru, zatímco Dolní jezera jsou dramatičtější s vyššími vodopády včetně nejvyššího - Veliki Slap (78 metrů).\n\nPark nabízí několik značených tras různé náročnosti. Nejpopulárnější je Program A (3-4 hodiny) a Program B (4-6 hodin), které zahrnují jízdu panoramatickým vlakem a plavbu elektrickým člunem po největším jezeře Kozjak. Voda v jezerech má jedinečnou tyrkysovou až smaragdově zelenou barvu díky minerálům a mikroorganismům.\n\nNejlepší doba návštěvy je od dubna do října, kdy jsou všechny trasy otevřené. V zimě park nabízí kouzelnou atmosféru s částečně zamrzlými vodopády, ale některé trasy mohou být uzavřené.',
          latitude: 44.8654,
          longitude: 15.5820,
          region: 'lika',
          type: PlaceType.park,
          images: ['plitvice1.jpg', 'plitvice2.jpg'],
          isVisited: false,
          rating: 4.9,
          tags: [
            'národní park',
            'příroda',
            'vodopády',
            'UNESCO',
            'jezera',
            'turistika',
          ],
          additionalInfo: {
            'opening_hours': '7:00-20:00 (léto), 8:00-16:00 (zima)',
            'entrance_fee': '10-40 EUR podle sezóny a věku',
            'duration': '4-8 hodin podle zvolené trasy',
            'routes': 'Program A (3-4h), Program B (4-6h), Program C (6-8h)',
            'highlights': 'Veliki Slap, jezero Kozjak, Horní jezera',
            'tips': 'Rezervujte online, noste pohodlnou obuv, plavání zakázáno',
            'best_time': 'Duben-říjen, nejkrásnější na podzim',
          },
        ),
        Place(
          id: '3',
          name: 'Zlatni Rat - Bol',
          description:
              'Zlatni Rat (Zlatý roh) je nejfotografovanější a nejslavnější pláž v Chorvatsku, nacházející se na jižním pobřeží ostrova Brač. Tato jedinečná pláž má tvar trojúhelníku, který se táhne 500 metrů do křišťálově čistého Jaderského moře.\n\nNejpozoruhodnější vlastností Zlatni Rat je její schopnost měnit tvar podle směru větru, proudů a vln. Špička pláže se může natáčet doleva nebo doprava, což vytváří stále se měnící přírodní umělecké dílo. Pláž je pokryta jemnými bílými oblázky, které jsou příjemné na bosé nohy.\n\nZlatni Rat je rájem pro vodní sporty, zejména windsurfing a kitesurfing díky ideálním větrným podmínkám. Maestral (severozápadní vítr) fouká odpoledne a vytváří perfektní podmínky pro surfování. Na pláži najdete půjčovny sportovního vybavení a surfové školy.\n\nPláž je obklopena borovým lesem, který poskytuje přirozený stín a příjemnou vůni. V blízkosti se nachází město Bol s restauracemi, bary a ubytováním. Nejlepší výhled na pláž máte z vrcholu hory Vidova Gora (778 m), nejvyššího bodu všech chorvatských ostrovů.',
          latitude: 43.2567,
          longitude: 16.6378,
          region: 'dalmatia',
          type: PlaceType.beach,
          images: ['zlatni_rat1.jpg'],
          isVisited: false,
          rating: 4.7,
          tags: [
            'pláž',
            'windsurfing',
            'ostrov Brač',
            'kitesurfing',
            'příroda',
            'fotografie',
          ],
          additionalInfo: {
            'opening_hours': '24/7 - veřejná pláž',
            'entrance_fee': 'Zdarma',
            'best_time': 'Červen-září pro koupání, duben-říjen pro návštěvu',
            'activities': 'Windsurfing, kitesurfing, paddleboarding, potápění',
            'facilities': 'Restaurace, bary, půjčovna lehátek, sprchy',
            'tips':
                'Přijďte ráno pro nejlepší místa, odpoledne ideální pro surfing',
            'transport': 'Ferry z Splitu do Supetar, pak bus do Bol',
          },
        ),
        Place(
          id: '4',
          name: 'Rovinj',
          description:
              'Rovinj je považován za nejromantičtější město v Istrii a jeden z nejkrásnějších přístavních měst na Jadranu. Toto bývalé rybářské městečko si zachovalo svůj autentický charakter s úzkými kamennými uličkami, barevnými domy a benátskou architekturou.\n\nDominantou města je kostel sv. Eufemie z 18. století, jehož 60metrová zvonice je viditelná z daleka a nabízí panoramatický výhled na město a okolní ostrovy. Staré město se nachází na poloostrově a je obklopeno křišťálově čistým mořem.\n\nRovinj je známý svou živou uměleckou scénou. V úzkých uličkách najdete galerie, ateliéry a obchody s místními řemeslnými výrobky. Město hostí mnoho kulturních akcí včetně Rovinj Photodays, mezinárodního festivalu fotografie.\n\nOkolí Rovinj nabízí krásné pláže, zejména Zlatni rt (Zlatý mys), lesnatý poloostrov s četnými zátokami a nudistickými plážemi. Město je také výbornou základnou pro objevování Istrie s jejími vinicemi, olivovými háji a truflovými lesy.',
          latitude: 45.0811,
          longitude: 13.6387,
          region: 'istria',
          type: PlaceType.monument,
          images: ['rovinj1.jpg'],
          isVisited: true,
          visitedDate: DateTime.now().subtract(const Duration(days: 15)),
          rating: 4.6,
          tags: [
            'Istrie',
            'romantika',
            'benátská architektura',
            'umění',
            'pláže',
            'fotografie',
          ],
          additionalInfo: {
            'best_time': 'Květen-září pro koupání, duben-říjen pro návštěvu',
            'highlights': 'Kostel sv. Eufemie, Staré město, Zlatni rt',
            'activities':
                'Potápění, cyklistika, degustace vín, umělecké galerie',
            'gastronomy':
                'Istrijska malvazija, trufle, olivový olej, mořské plody',
            'tips':
                'Rezervujte ubytování předem v sezóně, navštivte místní trh',
          },
        ),
        Place(
          id: '5',
          name: 'Krka National Park',
          description:
              'Národní park Krka se rozkládá podél řeky Krka a je známý svými sedmi vodopády, z nichž nejslavnější je Skradinski buk. Na rozdíl od Plitvických jezer je zde povoleno koupání v některých částech parku, což z něj činí oblíbenou letní destinaci.\n\nSkradinski buk je nejdelší a nejnavštěvovanější vodopád v parku s 17 stupni a celkovou výškou 45,7 metru. Vodopád vytváří přírodní bazény s tyrkysovou vodou, kde si můžete zaplavat obklopeni netušenou krásou přírody.\n\nPark nabízí také kulturní památky včetně kláštera na ostrůvku Visovac a etno vesnice Krka, kde můžete vidět tradiční způsob života a staré mlýny. Boat trip k vodopádu Roški slap a klášter Krka jsou další highlights parku.\n\nKrka je domovem bohaté flóry a fauny s více než 860 druhy rostlin a 220 druhy ptáků. Park je důležitým místem pro stěhování ptáků a můžete zde pozorovat vzácné druhy jako je orel mořský nebo čáp černý.',
          latitude: 43.8069,
          longitude: 15.9614,
          region: 'dalmatia',
          type: PlaceType.park,
          images: ['krka1.jpg'],
          isVisited: false,
          rating: 4.5,
          tags: [
            'národní park',
            'vodopády',
            'koupání',
            'příroda',
            'ptáci',
            'kláštery',
          ],
          additionalInfo: {
            'opening_hours': '8:00-20:00 (léto), 9:00-16:00 (zima)',
            'entrance_fee': '30-50 EUR podle sezóny',
            'swimming': 'Povoleno u Skradinski buk (červen-září)',
            'highlights': 'Skradinski buk, Visovac, Roški slap',
            'activities': 'Koupání, boat trips, hiking, bird watching',
            'tips': 'Přijďte ráno, noste plavky, respektujte přírodu',
          },
        ),
      ];

      _filteredPlaces = List.from(_allPlaces);
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Nepodařilo se načíst místa. Zkontrolujte připojení.';
      });
    }
  }

  void _filterPlaces() {
    List<Place> filtered = List.from(_allPlaces);

    // Filtrování podle vyhledávání
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((place) {
        return place.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            place.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            place.tags.any(
              (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
            );
      }).toList();
    }

    // Filtrování podle regionu
    if (_selectedRegion != 'all') {
      filtered = filtered
          .where((place) => place.region == _selectedRegion)
          .toList();
    }

    // Filtrování podle typu
    if (_selectedType != null) {
      filtered = filtered
          .where((place) => place.type == _selectedType)
          .toList();
    }

    // Filtrování navštívených míst
    if (_showVisitedOnly) {
      filtered = filtered.where((place) => place.isVisited).toList();
    }

    // Řazení podle vzdálenosti
    if (_sortByDistance) {
      _sortPlacesByDistance(filtered);
    } else {
      // Řazení podle hodnocení
      filtered.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));
    }

    setState(() {
      _filteredPlaces = filtered;
    });
  }

  Future<void> _sortPlacesByDistance(List<Place> places) async {
    try {
      final currentPosition = await _locationService.getCurrentPosition();

      places.sort((a, b) {
        final distanceA = _locationService.calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          a.latitude,
          a.longitude,
        );
        final distanceB = _locationService.calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          b.latitude,
          b.longitude,
        );
        return distanceA.compareTo(distanceB);
      });
    } catch (e) {
      debugPrint('Chyba při řazení podle vzdálenosti: $e');
    }
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => FilterWidget(
        selectedRegion: _selectedRegion,
        selectedType: _selectedType,
        showVisitedOnly: _showVisitedOnly,
        sortByDistance: _sortByDistance,
        onRegionChanged: (region) {
          setState(() {
            _selectedRegion = region;
          });
          _filterPlaces();
        },
        onTypeChanged: (type) {
          setState(() {
            _selectedType = type;
          });
          _filterPlaces();
        },
        onVisitedOnlyChanged: (value) {
          setState(() {
            _showVisitedOnly = value;
          });
          _filterPlaces();
        },
        onSortByDistanceChanged: (value) {
          setState(() {
            _sortByDistance = value;
          });
          _filterPlaces();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Místa k návštěvě',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFF006994).withValues(alpha: 0.9),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorPlacesHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _showFilterDialog,
              icon: const Icon(Icons.filter_list),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Vyhledávací pole s watercolor efektem
          Padding(
            padding: const EdgeInsets.all(16),
            child: Container(
              child: CustomPaint(
                painter: WatercolorSearchPainter(),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Vyhledat místa...',
                    hintStyle: GoogleFonts.inter(
                      color: const Color(0xFF666666),
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: const Color(0xFF006994),
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                              _filterPlaces();
                            },
                            icon: Icon(
                              Icons.clear,
                              color: const Color(0xFF666666),
                            ),
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF2E8B8B).withValues(alpha: 0.3),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF2E8B8B).withValues(alpha: 0.3),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF006994),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.white.withValues(alpha: 0.9),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    _filterPlaces();
                  },
                ),
              ),
            ),
          ),

          // Statistiky s watercolor efektem
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              padding: const EdgeInsets.all(16),
              child: CustomPaint(
                painter: WatercolorStatsPainter(),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildWatercolorStat(
                        'Nalezeno',
                        '${_filteredPlaces.length}',
                        'míst',
                        const Color(0xFF006994),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: _buildWatercolorStat(
                        'Navštíveno',
                        '${_allPlaces.where((p) => p.isVisited).length}',
                        'míst',
                        const Color(0xFF2E8B8B),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: _buildWatercolorStat(
                        'Hodnocení',
                        '${(_allPlaces.map((p) => p.rating ?? 0).reduce((a, b) => a + b) / _allPlaces.length).toStringAsFixed(1)}',
                        'průměr',
                        const Color(0xFFFF6B35),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Seznam míst
          Expanded(
            child: _filteredPlaces.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.location_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Žádná místa nenalezena',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Zkuste změnit filtry nebo vyhledávání',
                          style: TextStyle(color: Colors.grey[500]),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredPlaces.length,
                    itemBuilder: (context, index) {
                      final place = _filteredPlaces[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: PlaceCard(
                          place: place,
                          onTap: () => _navigateToPlaceDetail(place),
                          onVisitedToggle: () => _toggleVisited(place),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  void _navigateToPlaceDetail(Place place) {
    // Navigace na detail místa
    Navigator.pushNamed(context, '/places/${place.id}');
  }

  void _toggleVisited(Place place) {
    setState(() {
      final index = _allPlaces.indexWhere((p) => p.id == place.id);
      if (index != -1) {
        _allPlaces[index] = place.copyWith(
          isVisited: !place.isVisited,
          visitedDate: !place.isVisited ? DateTime.now() : null,
        );
      }
    });
    _filterPlaces();
  }

  // Watercolor statistika
  Widget _buildWatercolorStat(
    String label,
    String value,
    String unit,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: CustomPaint(
        painter: WatercolorPlacesStatPainter(color),
        child: Column(
          children: [
            Text(
              value,
              style: GoogleFonts.playfairDisplay(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              unit,
              style: GoogleFonts.inter(
                fontSize: 10,
                color: color.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2C2C2C),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

// Watercolor painters pro Places Screen
class WatercolorPlacesHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Places header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.4);
    path1.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.6,
      size.height * 0.5,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.8,
      size.width,
      size.height * 0.3,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.6);
    path2.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.3,
      size.width * 0.7,
      size.height * 0.7,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.9,
      size.width,
      size.height * 0.5,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.1);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSearchPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt kolem vyhledávacího pole
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.25,
      size.width * 0.9,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.5,
      size.width * 0.05,
      size.height * 0.2,
    );
    path.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorStatsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor pozadí pro statistiky
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.3);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.1,
      size.width * 0.9,
      size.height * 0.2,
    );
    path.lineTo(size.width * 0.85, size.height * 0.7);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.9,
      size.width * 0.15,
      size.height * 0.8,
    );
    path.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.05);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorPlacesStatPainter extends CustomPainter {
  final Color color;

  WatercolorPlacesStatPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Malý watercolor kruh kolem statistiky
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 3.5;

    // Organický tvar kolem čísla
    final path = Path();
    for (int i = 0; i < 360; i += 25) {
      final angle = i * pi / 180;
      final variation = 0.6 + (sin(i * pi / 45) * 0.4);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
